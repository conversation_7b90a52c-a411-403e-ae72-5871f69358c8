"""
語音錄音模組
處理 Discord 語音頻道的錄音功能
"""
import asyncio
import discord
import wave
import io
from datetime import datetime
from pathlib import Path
from loguru import logger
import uuid

from config.settings import settings

class VoiceRecorder:
    """語音錄音器"""
    
    def __init__(self):
        self.is_recording = False
        self.current_meeting_id = None
        self.audio_data = []
        self.start_time = None
        self.participants = set()
        
    async def start_recording(self, voice_client, channel):
        """開始錄音"""
        if self.is_recording:
            raise Exception("已經在錄音中")
        
        # 生成會議 ID
        self.current_meeting_id = f"meeting_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{str(uuid.uuid4())[:8]}"
        self.start_time = datetime.now()
        self.is_recording = True
        self.audio_data = []
        self.participants = set()
        
        # 記錄初始參與者
        for member in channel.members:
            if not member.bot:
                self.participants.add(member.display_name)
        
        logger.info(f"開始錄音 - 會議ID: {self.current_meeting_id}")
        logger.info(f"初始參與者: {', '.join(self.participants)}")
        
        # 開始錄音（使用 Discord.py 的 sink）
        voice_client.start_recording(
            discord.sinks.WaveSink(),
            self._recording_finished_callback,
            channel
        )
        
        return self.current_meeting_id
    
    async def stop_recording(self):
        """停止錄音"""
        if not self.is_recording:
            raise Exception("目前沒有在錄音")
        
        self.is_recording = False
        end_time = datetime.now()
        duration = end_time - self.start_time
        
        logger.info(f"停止錄音 - 會議ID: {self.current_meeting_id}")
        logger.info(f"錄音時長: {duration}")
        logger.info(f"參與者: {', '.join(self.participants)}")
        
        # 保存錄音檔案
        audio_file = await self._save_audio_file()
        
        # 保存會議資訊
        await self._save_meeting_info(duration, end_time)
        
        return audio_file
    
    def _recording_finished_callback(self, sink, channel, *args):
        """錄音完成回調"""
        logger.info("錄音回調觸發")
        
        # 處理每個用戶的音訊
        for user_id, audio in sink.audio_data.items():
            # 將音訊數據添加到列表中
            self.audio_data.append({
                'user_id': user_id,
                'audio': audio,
                'timestamp': datetime.now()
            })
    
    async def _save_audio_file(self):
        """保存音訊檔案"""
        if not self.current_meeting_id:
            raise Exception("沒有有效的會議 ID")
        
        # 建立檔案路徑
        audio_file = settings.RECORDINGS_DIR / f"{self.current_meeting_id}.wav"
        
        try:
            # 如果有音訊數據，合併並保存
            if self.audio_data:
                await self._merge_and_save_audio(audio_file)
            else:
                # 建立空的音訊檔案作為佔位符
                with wave.open(str(audio_file), 'wb') as wav_file:
                    wav_file.setnchannels(settings.RECORDING_CHANNELS)
                    wav_file.setsampwidth(2)  # 16-bit
                    wav_file.setframerate(settings.RECORDING_SAMPLE_RATE)
                    wav_file.writeframes(b'')
            
            logger.info(f"音訊檔案已保存: {audio_file}")
            return audio_file
            
        except Exception as e:
            logger.error(f"保存音訊檔案失敗: {e}")
            raise
    
    async def _merge_and_save_audio(self, output_file):
        """合併並保存音訊數據"""
        try:
            # 開啟輸出檔案
            with wave.open(str(output_file), 'wb') as output_wav:
                output_wav.setnchannels(settings.RECORDING_CHANNELS)
                output_wav.setsampwidth(2)  # 16-bit
                output_wav.setframerate(settings.RECORDING_SAMPLE_RATE)
                
                # 合併所有音訊數據
                for audio_entry in self.audio_data:
                    audio_data = audio_entry['audio']
                    if hasattr(audio_data, 'file'):
                        # 讀取音訊數據
                        audio_data.file.seek(0)
                        audio_bytes = audio_data.file.read()
                        
                        # 寫入合併檔案
                        if audio_bytes:
                            output_wav.writeframes(audio_bytes)
            
            logger.info(f"成功合併 {len(self.audio_data)} 個音訊片段")
            
        except Exception as e:
            logger.error(f"合併音訊失敗: {e}")
            # 建立空檔案作為備用
            with wave.open(str(output_file), 'wb') as wav_file:
                wav_file.setnchannels(settings.RECORDING_CHANNELS)
                wav_file.setsampwidth(2)
                wav_file.setframerate(settings.RECORDING_SAMPLE_RATE)
                wav_file.writeframes(b'')
    
    async def _save_meeting_info(self, duration, end_time):
        """保存會議資訊"""
        meeting_info = {
            'meeting_id': self.current_meeting_id,
            'start_time': self.start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'duration_seconds': duration.total_seconds(),
            'participants': list(self.participants),
            'audio_segments': len(self.audio_data)
        }
        
        # 保存到 JSON 檔案
        import json
        info_file = settings.RECORDINGS_DIR / f"{self.current_meeting_id}_info.json"
        
        try:
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(meeting_info, f, ensure_ascii=False, indent=2)
            
            logger.info(f"會議資訊已保存: {info_file}")
            
        except Exception as e:
            logger.error(f"保存會議資訊失敗: {e}")
    
    def add_participant(self, username):
        """添加參與者"""
        if username and not username.startswith('Bot'):
            self.participants.add(username)
            logger.debug(f"添加參與者: {username}")
    
    def get_current_meeting_info(self):
        """獲取當前會議資訊"""
        if not self.is_recording:
            return None
        
        current_duration = datetime.now() - self.start_time if self.start_time else None
        
        return {
            'meeting_id': self.current_meeting_id,
            'start_time': self.start_time,
            'duration': current_duration,
            'participants': list(self.participants),
            'is_recording': self.is_recording
        }

class AudioSink(discord.sinks.Sink):
    """自定義音訊接收器"""
    
    def __init__(self, recorder):
        super().__init__()
        self.recorder = recorder
        self.audio_data = {}
    
    def write(self, data, user):
        """寫入音訊數據"""
        if user not in self.audio_data:
            self.audio_data[user] = io.BytesIO()
        
        self.audio_data[user].write(data)
        
        # 添加參與者
        if hasattr(user, 'display_name'):
            self.recorder.add_participant(user.display_name)
    
    def cleanup(self):
        """清理資源"""
        for audio_stream in self.audio_data.values():
            audio_stream.close()
        self.audio_data.clear()
