#!/usr/bin/env python3
"""
Discord Meeting Bot 安裝測試腳本
檢查所有依賴和設定是否正確
"""
import sys
import os
import subprocess
from pathlib import Path

def check_python_version():
    """檢查 Python 版本"""
    print("🐍 檢查 Python 版本...")
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} - 版本符合要求")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} - 需要 Python 3.8+")
        return False

def check_ffmpeg():
    """檢查 FFmpeg 安裝"""
    print("\n🎵 檢查 FFmpeg...")
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            print(f"✅ {version_line}")
            return True
        else:
            print("❌ FFmpeg 未正確安裝")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("❌ 找不到 FFmpeg，請安裝 FFmpeg")
        return False

def check_dependencies():
    """檢查 Python 依賴套件"""
    print("\n📦 檢查 Python 依賴套件...")
    
    required_packages = [
        'discord.py',
        'loguru',
        'python-dotenv',
        'aiohttp',
        'requests'
    ]
    
    optional_packages = [
        'faster-whisper',
        'openai-whisper',
        'vosk',
        'pydub'
    ]
    
    all_good = True
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - 必要套件未安裝")
            all_good = False
    
    print("\n📦 檢查可選套件...")
    for package in optional_packages:
        try:
            if package == 'openai-whisper':
                import whisper
                print(f"✅ {package}")
            elif package == 'faster-whisper':
                from faster_whisper import WhisperModel
                print(f"✅ {package}")
            else:
                __import__(package.replace('-', '_'))
                print(f"✅ {package}")
        except ImportError:
            print(f"⚠️  {package} - 可選套件未安裝")
    
    return all_good

def check_directories():
    """檢查目錄結構"""
    print("\n📁 檢查目錄結構...")
    
    required_dirs = [
        'config',
        'modules',
        'prompts',
        'data',
        'data/recordings',
        'data/transcripts',
        'data/meetings',
        'logs'
    ]
    
    all_good = True
    for dir_path in required_dirs:
        path = Path(dir_path)
        if path.exists():
            print(f"✅ {dir_path}/")
        else:
            print(f"❌ {dir_path}/ - 目錄不存在")
            all_good = False
    
    return all_good

def check_config_files():
    """檢查設定檔案"""
    print("\n⚙️  檢查設定檔案...")
    
    config_files = [
        ('config/.env.example', '環境變數範例檔'),
        ('prompts/system_prompt.txt', '系統提示詞'),
        ('requirements.txt', '依賴套件列表'),
        ('bot.py', '主程式')
    ]
    
    all_good = True
    for file_path, description in config_files:
        path = Path(file_path)
        if path.exists():
            print(f"✅ {file_path} - {description}")
        else:
            print(f"❌ {file_path} - {description} 不存在")
            all_good = False
    
    # 檢查 .env 檔案
    env_file = Path('config/.env')
    if env_file.exists():
        print("✅ config/.env - 環境變數設定檔")
        
        # 檢查必要的環境變數
        with open(env_file, 'r') as f:
            content = f.read()
            required_vars = ['DISCORD_TOKEN', 'OUTPUT_CHANNEL_ID']
            for var in required_vars:
                if var in content and not content.count(f'{var}=your_') > 0:
                    print(f"✅ {var} 已設定")
                else:
                    print(f"⚠️  {var} 需要設定")
    else:
        print("⚠️  config/.env - 請複製 .env.example 並設定")
    
    return all_good

def check_ollama():
    """檢查 Ollama 連接"""
    print("\n🧠 檢查 Ollama 連接...")
    
    try:
        import requests
        response = requests.get('http://localhost:11434/api/tags', timeout=5)
        if response.status_code == 200:
            data = response.json()
            models = [model['name'] for model in data.get('models', [])]
            if models:
                print(f"✅ Ollama 連接成功，可用模型: {', '.join(models)}")
                return True
            else:
                print("⚠️  Ollama 連接成功，但沒有可用模型")
                print("   請執行: ollama pull llama3:8b")
                return False
        else:
            print(f"❌ Ollama API 回應錯誤: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print("❌ 無法連接到 Ollama")
        print("   請確認 Ollama 正在運行: ollama serve")
        return False

def main():
    """主測試函數"""
    print("🤖 Discord Meeting Bot 安裝檢查")
    print("=" * 50)
    
    checks = [
        ("Python 版本", check_python_version),
        ("FFmpeg", check_ffmpeg),
        ("Python 依賴", check_dependencies),
        ("目錄結構", check_directories),
        ("設定檔案", check_config_files),
        ("Ollama 連接", check_ollama)
    ]
    
    results = []
    for name, check_func in checks:
        try:
            result = check_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name} 檢查時發生錯誤: {e}")
            results.append((name, False))
    
    print("\n" + "=" * 50)
    print("📊 檢查結果摘要:")
    
    all_passed = True
    for name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"{name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有檢查都通過！Bot 已準備就緒。")
        print("\n下一步:")
        print("1. 設定 config/.env 檔案中的 Discord Token")
        print("2. 執行: python bot.py")
    else:
        print("⚠️  有些檢查未通過，請參考上述訊息進行修正。")
        print("\n參考文件:")
        print("- INSTALL.md - 安裝指南")
        print("- TROUBLESHOOTING.md - 故障排除")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
