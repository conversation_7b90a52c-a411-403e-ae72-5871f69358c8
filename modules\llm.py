"""
LLM 整合模組
支援 Ollama 本地 LLM 進行會議摘要生成
"""
import asyncio
import json
import aiohttp
from datetime import datetime
from pathlib import Path
from loguru import logger

from config.settings import settings

class LLMProcessor:
    """LLM 處理器"""
    
    def __init__(self):
        self.backend_url = settings.MODEL_BACKEND
        self.model_name = settings.LLM_MODEL
        self.temperature = settings.LLM_TEMPERATURE
        self.system_prompt = self._load_system_prompt()
        
    def _load_system_prompt(self):
        """載入系統提示詞"""
        prompt_file = settings.PROMPTS_DIR / "system_prompt.txt"
        
        try:
            with open(prompt_file, 'r', encoding='utf-8') as f:
                prompt = f.read().strip()
            logger.info("系統提示詞載入成功")
            return prompt
        except FileNotFoundError:
            logger.warning(f"系統提示詞檔案不存在: {prompt_file}")
            return self._get_default_prompt()
        except Exception as e:
            logger.error(f"載入系統提示詞失敗: {e}")
            return self._get_default_prompt()
    
    def _get_default_prompt(self):
        """獲取預設提示詞"""
        return """你是一個專業的會議紀錄助手。請根據以下的會議轉錄文字，生成一份結構化的會議紀錄。

請按照以下格式輸出：

# 會議紀錄
- 日期：{date}
- 與會人員：{participants}
- 會議時長：{duration}

## 會議摘要
（用 2-3 句話概括整場會議的主要內容）

## 討論要點
1. 主題一：具體討論內容
2. 主題二：具體討論內容
3. 主題三：具體討論內容

## 重要決策
- 決策一：具體決策內容和背景
- 決策二：具體決策內容和背景

## 待辦事項 (Action Items)
- 負責人：具體任務描述 (截止日期)
- 負責人：具體任務描述 (截止日期)

## 後續追蹤
- 下次會議時間：
- 需要準備的資料：
- 其他注意事項：

請保持客觀中性，忠實記錄討論內容，重點突出決策和行動項目。"""
    
    async def generate_summary(self, transcript, meeting_id):
        """生成會議摘要"""
        if not transcript or not transcript.get('text'):
            logger.warning("轉錄文字為空，無法生成摘要")
            return "無法生成會議摘要：轉錄文字為空"
        
        logger.info(f"開始生成會議摘要 - 會議ID: {meeting_id}")
        
        try:
            # 準備會議資訊
            meeting_info = await self._get_meeting_info(meeting_id)
            
            # 構建提示詞
            prompt = self._build_prompt(transcript, meeting_info)
            
            # 呼叫 LLM
            summary = await self._call_llm(prompt)
            
            # 保存摘要
            await self._save_summary(meeting_id, summary, transcript)
            
            logger.info(f"會議摘要生成完成 - 會議ID: {meeting_id}")
            return summary
            
        except Exception as e:
            logger.error(f"生成會議摘要失敗: {e}")
            return f"生成會議摘要時發生錯誤: {str(e)}"
    
    async def _get_meeting_info(self, meeting_id):
        """獲取會議資訊"""
        info_file = settings.RECORDINGS_DIR / f"{meeting_id}_info.json"
        
        try:
            if info_file.exists():
                with open(info_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                logger.warning(f"會議資訊檔案不存在: {info_file}")
                return {}
        except Exception as e:
            logger.error(f"讀取會議資訊失敗: {e}")
            return {}
    
    def _build_prompt(self, transcript, meeting_info):
        """構建 LLM 提示詞"""
        # 格式化會議資訊
        date = datetime.now().strftime('%Y/%m/%d')
        participants = ', '.join(meeting_info.get('participants', ['未知']))
        duration_seconds = meeting_info.get('duration_seconds', 0)
        duration = f"{int(duration_seconds // 60)}分{int(duration_seconds % 60)}秒"
        
        # 替換系統提示詞中的佔位符
        formatted_prompt = self.system_prompt.format(
            date=date,
            participants=participants,
            duration=duration
        )
        
        # 構建完整提示詞
        full_prompt = f"""{formatted_prompt}

以下是會議的轉錄文字：

{transcript['text']}

請根據以上內容生成結構化的會議紀錄："""
        
        return full_prompt
    
    async def _call_llm(self, prompt):
        """呼叫 LLM API"""
        if self.backend_url.endswith('/api/generate'):
            return await self._call_ollama_api(prompt)
        else:
            return await self._call_ollama_api(prompt)
    
    async def _call_ollama_api(self, prompt):
        """呼叫 Ollama API"""
        api_url = f"{self.backend_url.rstrip('/')}/api/generate"
        
        payload = {
            "model": self.model_name,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": self.temperature,
                "top_p": 0.9,
                "top_k": 40
            }
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    api_url,
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=300)  # 5分鐘超時
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result.get('response', '無法生成摘要')
                    else:
                        error_text = await response.text()
                        logger.error(f"LLM API 錯誤 {response.status}: {error_text}")
                        return f"LLM API 錯誤: {response.status}"
                        
        except asyncio.TimeoutError:
            logger.error("LLM API 請求超時")
            return "生成摘要超時，請稍後再試"
        except Exception as e:
            logger.error(f"呼叫 LLM API 失敗: {e}")
            return f"呼叫 LLM 失敗: {str(e)}"
    
    async def _save_summary(self, meeting_id, summary, transcript):
        """保存會議摘要"""
        summary_data = {
            'meeting_id': meeting_id,
            'timestamp': datetime.now().isoformat(),
            'llm_model': self.model_name,
            'temperature': self.temperature,
            'summary': summary,
            'original_transcript': transcript.get('text', ''),
            'transcript_segments': len(transcript.get('segments', []))
        }
        
        summary_file = settings.MEETINGS_DIR / f"{meeting_id}_summary.json"
        
        try:
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(summary_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"會議摘要已保存: {summary_file}")
            
        except Exception as e:
            logger.error(f"保存會議摘要失敗: {e}")
    
    async def test_connection(self):
        """測試 LLM 連接"""
        try:
            test_prompt = "請回答：你好"
            response = await self._call_llm(test_prompt)
            
            if response and "你好" in response:
                logger.info("LLM 連接測試成功")
                return True
            else:
                logger.warning(f"LLM 連接測試異常，回應: {response}")
                return False
                
        except Exception as e:
            logger.error(f"LLM 連接測試失敗: {e}")
            return False
    
    async def get_available_models(self):
        """獲取可用的模型列表"""
        api_url = f"{self.backend_url.rstrip('/')}/api/tags"
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(api_url) as response:
                    if response.status == 200:
                        result = await response.json()
                        models = [model['name'] for model in result.get('models', [])]
                        logger.info(f"可用模型: {models}")
                        return models
                    else:
                        logger.error(f"獲取模型列表失敗: {response.status}")
                        return []
                        
        except Exception as e:
            logger.error(f"獲取模型列表失敗: {e}")
            return []
    
    def update_system_prompt(self, new_prompt):
        """更新系統提示詞"""
        self.system_prompt = new_prompt
        
        # 保存到檔案
        prompt_file = settings.PROMPTS_DIR / "system_prompt.txt"
        try:
            with open(prompt_file, 'w', encoding='utf-8') as f:
                f.write(new_prompt)
            logger.info("系統提示詞已更新")
        except Exception as e:
            logger.error(f"保存系統提示詞失敗: {e}")
    
    def get_llm_info(self):
        """獲取 LLM 資訊"""
        return {
            'backend_url': self.backend_url,
            'model_name': self.model_name,
            'temperature': self.temperature,
            'system_prompt_length': len(self.system_prompt)
        }
