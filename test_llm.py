#!/usr/bin/env python3
"""
測試 LLM 連接
"""
import asyncio
from modules.llm import LLMProcessor

async def test_llm():
    """測試 LLM 連接和功能"""
    print("🧠 測試 LLM 連接...")
    
    llm = LLMProcessor()
    
    # 測試連接
    connection_result = await llm.test_connection()
    print(f"連接測試: {'✅ 成功' if connection_result else '❌ 失敗'}")
    
    # 獲取可用模型
    models = await llm.get_available_models()
    print(f"可用模型: {models}")
    
    # 測試簡單的摘要生成
    if connection_result:
        print("\n測試摘要生成...")
        test_transcript = {
            'text': '大家好，今天我們討論專案進度。Alice 說後端 API 已經完成 80%，Bob 提到前端還需要一週時間。我們決定下週二開會檢討。',
            'segments': []
        }
        
        summary = await llm.generate_summary(test_transcript, 'test_meeting_001')
        print(f"生成的摘要:\n{summary}")

if __name__ == "__main__":
    asyncio.run(test_llm())
