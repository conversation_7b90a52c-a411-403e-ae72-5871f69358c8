# Discord Meeting Bot 安裝指南

## 📋 系統需求

### 基本需求
- Python 3.8 或更高版本
- FFmpeg（音訊處理）
- 至少 4GB RAM
- 10GB 可用磁碟空間

### 推薦需求
- Python 3.11
- 8GB RAM 或更多
- GPU 支援（用於 Faster-Whisper 加速）
- SSD 儲存空間

## 🚀 快速安裝

### Windows

1. **下載並安裝 Python**
   - 從 [python.org](https://www.python.org/downloads/) 下載 Python 3.8+
   - 安裝時勾選 "Add Python to PATH"

2. **安裝 FFmpeg**
   - 從 [ffmpeg.org](https://ffmpeg.org/download.html) 下載
   - 解壓縮並將 `bin` 目錄加入系統 PATH

3. **執行安裝腳本**
   ```cmd
   install.bat
   ```

### Linux/macOS

1. **安裝依賴**
   ```bash
   # Ubuntu/Debian
   sudo apt update
   sudo apt install python3 python3-pip python3-venv ffmpeg git
   
   # macOS (使用 Homebrew)
   brew install python ffmpeg git
   ```

2. **執行安裝腳本**
   ```bash
   chmod +x scripts/install.sh
   ./scripts/install.sh
   ```

## 🔧 手動安裝

### 1. 克隆專案
```bash
git clone https://github.com/yourusername/discord-meeting-bot.git
cd discord-meeting-bot
```

### 2. 建立虛擬環境
```bash
python -m venv venv

# Windows
venv\Scripts\activate

# Linux/macOS
source venv/bin/activate
```

### 3. 安裝依賴
```bash
pip install -r requirements.txt
```

### 4. 設定環境變數
```bash
cp config/.env.example config/.env
```

編輯 `config/.env` 檔案，設定必要參數：
```env
DISCORD_TOKEN=your_discord_bot_token_here
OUTPUT_CHANNEL_ID=your_output_channel_id_here
MODEL_BACKEND=http://localhost:11434
STT_ENGINE=whisper
```

## 🤖 Discord Bot 設定

### 1. 建立 Discord 應用程式
1. 前往 [Discord Developer Portal](https://discord.com/developers/applications)
2. 點擊 "New Application"
3. 輸入應用程式名稱

### 2. 建立 Bot
1. 在左側選單點擊 "Bot"
2. 點擊 "Add Bot"
3. 複製 Bot Token 到 `.env` 檔案

### 3. 設定權限
在 "OAuth2" > "URL Generator" 中選擇：
- Scopes: `bot`
- Bot Permissions:
  - Send Messages
  - Use Slash Commands
  - Connect
  - Speak
  - Use Voice Activity

### 4. 邀請 Bot 到伺服器
使用生成的 URL 邀請 Bot 到您的 Discord 伺服器

## 🧠 LLM 後端設定

### 選項 1: Ollama（推薦）

1. **安裝 Ollama**
   ```bash
   # Linux
   curl -fsSL https://ollama.ai/install.sh | sh
   
   # Windows/macOS
   # 從 https://ollama.ai 下載安裝程式
   ```

2. **下載模型**
   ```bash
   ollama pull llama3:8b
   # 或其他支援的模型
   ollama pull mistral:7b
   ollama pull gemma:7b
   ```

3. **啟動 Ollama**
   ```bash
   ollama serve
   ```

### 選項 2: Docker 部署

```bash
docker-compose up -d
```

這會同時啟動 Bot 和 Ollama 服務。

## 🎵 STT 引擎設定

### Whisper（預設）
無需額外設定，會自動下載模型。

### Faster-Whisper（推薦，效能更好）
```bash
pip install faster-whisper
```

### Vosk（輕量級）
1. 下載語言模型：
   ```bash
   mkdir models
   cd models
   wget https://alphacephei.com/vosk/models/vosk-model-cn-0.22.zip
   unzip vosk-model-cn-0.22.zip
   mv vosk-model-cn-0.22 vosk-model
   ```

## 🚀 啟動 Bot

### 開發模式
```bash
python bot.py
```

### 生產模式（Docker）
```bash
docker-compose up -d
```

### 檢查狀態
```bash
# 查看日誌
tail -f logs/bot.log

# Docker 日誌
docker-compose logs -f discord-bot
```

## 🔍 故障排除

### 常見問題

1. **Bot 無法連接到 Discord**
   - 檢查 Discord Token 是否正確
   - 確認 Bot 有適當的權限

2. **語音錄音失敗**
   - 確認 FFmpeg 已正確安裝
   - 檢查 Bot 是否有語音頻道權限

3. **STT 轉換失敗**
   - 檢查音訊檔案是否存在
   - 確認 STT 引擎已正確安裝

4. **LLM 摘要生成失敗**
   - 確認 Ollama 服務正在運行
   - 檢查模型是否已下載
   - 驗證 API 連接

### 日誌檢查
```bash
# 查看詳細日誌
tail -f logs/bot.log

# 檢查特定錯誤
grep "ERROR" logs/bot.log
```

### 測試連接
```python
# 測試 LLM 連接
python -c "
import asyncio
from modules.llm import LLMProcessor
async def test():
    llm = LLMProcessor()
    result = await llm.test_connection()
    print(f'LLM 連接: {result}')
asyncio.run(test())
"
```

## 📞 支援

如果遇到問題，請：

1. 檢查 [故障排除指南](TROUBLESHOOTING.md)
2. 查看 [常見問題](FAQ.md)
3. 提交 [Issue](https://github.com/yourusername/discord-meeting-bot/issues)

## 🔄 更新

```bash
git pull origin main
pip install -r requirements.txt --upgrade
```

Docker 部署：
```bash
docker-compose pull
docker-compose up -d
```
