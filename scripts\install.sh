#!/bin/bash

echo "Discord Meeting Bot 安裝腳本"
echo "============================"

# 檢查 Python 版本
echo "1. 檢查 Python 版本..."
if ! command -v python3 &> /dev/null; then
    echo "錯誤: 找不到 Python3，請先安裝 Python 3.8+"
    exit 1
fi

python3 --version

# 建立虛擬環境
echo "2. 建立虛擬環境..."
python3 -m venv venv
if [ $? -ne 0 ]; then
    echo "錯誤: 無法建立虛擬環境"
    exit 1
fi

# 啟動虛擬環境
echo "3. 啟動虛擬環境..."
source venv/bin/activate

# 升級 pip
echo "4. 升級 pip..."
python -m pip install --upgrade pip

# 安裝依賴套件
echo "5. 安裝依賴套件..."
pip install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "錯誤: 安裝依賴套件失敗"
    exit 1
fi

# 複製環境設定檔
echo "6. 複製環境設定檔..."
if [ ! -f config/.env ]; then
    cp config/.env.example config/.env
    echo "請編輯 config/.env 檔案，設定您的 Discord Token 和其他參數"
fi

# 檢查 FFmpeg
echo "7. 檢查 FFmpeg..."
if ! command -v ffmpeg &> /dev/null; then
    echo "警告: 找不到 FFmpeg，請安裝 FFmpeg"
    echo "Ubuntu/Debian: sudo apt install ffmpeg"
    echo "macOS: brew install ffmpeg"
fi

echo ""
echo "安裝完成！"
echo ""
echo "下一步："
echo "1. 編輯 config/.env 檔案，設定您的 Discord Token"
echo "2. 確保 Ollama 正在運行 (http://localhost:11434)"
echo "3. 執行 python bot.py 啟動 Bot"
echo ""
