version: '3.8'

services:
  discord-bot:
    build: .
    container_name: discord-meeting-bot
    restart: unless-stopped
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./config/.env:/app/config/.env
      - ./prompts:/app/prompts
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
    depends_on:
      - ollama
    networks:
      - meeting-bot-network

  ollama:
    image: ollama/ollama:latest
    container_name: ollama-server
    restart: unless-stopped
    ports:
      - "11434:11434"
    volumes:
      - ollama-data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0
    networks:
      - meeting-bot-network
    # 如果有 GPU 支援，取消註解以下行
    # deploy:
    #   resources:
    #     reservations:
    #       devices:
    #         - driver: nvidia
    #           count: 1
    #           capabilities: [gpu]

volumes:
  ollama-data:
    driver: local

networks:
  meeting-bot-network:
    driver: bridge
