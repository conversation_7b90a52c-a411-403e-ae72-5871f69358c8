# Discord Meeting Bot Dockerfile
FROM python:3.11-slim

# 設定工作目錄
WORKDIR /app

# 安裝系統依賴
RUN apt-get update && apt-get install -y \
    ffmpeg \
    git \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 複製需求檔案
COPY requirements.txt .

# 安裝 Python 依賴
RUN pip install --no-cache-dir -r requirements.txt

# 複製應用程式碼
COPY . .

# 建立必要的目錄
RUN mkdir -p data/recordings data/transcripts data/meetings logs

# 設定環境變數
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# 暴露埠號（如果需要）
# EXPOSE 8000

# 建立非 root 使用者
RUN useradd -m -u 1000 botuser && chown -R botuser:botuser /app
USER botuser

# 啟動指令
CMD ["python", "bot.py"]
