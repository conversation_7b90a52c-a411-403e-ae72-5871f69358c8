"""
語音轉文字 (STT) 模組
支援 Whisper、Faster-Whisper 和 Vosk 引擎
"""
import asyncio
import json
from pathlib import Path
from datetime import datetime
from loguru import logger
import wave
import os

from config.settings import settings

class STTProcessor:
    """語音轉文字處理器"""
    
    def __init__(self):
        self.engine = settings.STT_ENGINE.lower()
        self.model = None
        self._initialize_engine()
    
    def _initialize_engine(self):
        """初始化 STT 引擎"""
        try:
            if self.engine == 'whisper':
                self._initialize_whisper()
            elif self.engine == 'faster-whisper':
                self._initialize_faster_whisper()
            elif self.engine == 'vosk':
                self._initialize_vosk()
            else:
                raise ValueError(f"不支援的 STT 引擎: {self.engine}")
            
            logger.info(f"STT 引擎初始化成功: {self.engine}")
            
        except Exception as e:
            logger.error(f"STT 引擎初始化失敗: {e}")
            raise
    
    def _initialize_whisper(self):
        """初始化 OpenAI Whisper"""
        try:
            import whisper
            self.model = whisper.load_model(settings.WHISPER_MODEL)
            logger.info(f"Whisper 模型載入成功: {settings.WHISPER_MODEL}")
        except ImportError:
            raise ImportError("請安裝 openai-whisper: pip install openai-whisper")
    
    def _initialize_faster_whisper(self):
        """初始化 Faster-Whisper"""
        try:
            from faster_whisper import WhisperModel
            
            # 檢查是否有 GPU 支援
            device = "cuda" if self._check_gpu_support() else "cpu"
            compute_type = "float16" if device == "cuda" else "int8"
            
            self.model = WhisperModel(
                settings.WHISPER_MODEL,
                device=device,
                compute_type=compute_type
            )
            logger.info(f"Faster-Whisper 模型載入成功: {settings.WHISPER_MODEL} ({device})")
        except ImportError:
            raise ImportError("請安裝 faster-whisper: pip install faster-whisper")
    
    def _initialize_vosk(self):
        """初始化 Vosk"""
        try:
            import vosk
            
            # 檢查模型檔案是否存在
            model_path = Path("models") / "vosk-model"
            if not model_path.exists():
                logger.warning("Vosk 模型不存在，請下載對應語言的模型")
                raise FileNotFoundError(f"Vosk 模型路徑不存在: {model_path}")
            
            self.model = vosk.Model(str(model_path))
            logger.info("Vosk 模型載入成功")
        except ImportError:
            raise ImportError("請安裝 vosk: pip install vosk")
    
    def _check_gpu_support(self):
        """檢查 GPU 支援"""
        try:
            import torch
            return torch.cuda.is_available()
        except ImportError:
            return False
    
    async def process_audio(self, audio_file):
        """處理音訊檔案，轉換為文字"""
        if not audio_file.exists():
            raise FileNotFoundError(f"音訊檔案不存在: {audio_file}")
        
        logger.info(f"開始處理音訊檔案: {audio_file}")
        
        try:
            # 根據引擎選擇處理方法
            if self.engine == 'whisper':
                transcript = await self._process_with_whisper(audio_file)
            elif self.engine == 'faster-whisper':
                transcript = await self._process_with_faster_whisper(audio_file)
            elif self.engine == 'vosk':
                transcript = await self._process_with_vosk(audio_file)
            else:
                raise ValueError(f"不支援的引擎: {self.engine}")
            
            # 保存轉錄結果
            await self._save_transcript(audio_file, transcript)
            
            logger.info(f"語音轉文字完成，文字長度: {len(transcript['text'])} 字元")
            return transcript
            
        except Exception as e:
            logger.error(f"語音轉文字失敗: {e}")
            raise
    
    async def _process_with_whisper(self, audio_file):
        """使用 Whisper 處理音訊"""
        def _transcribe():
            result = self.model.transcribe(
                str(audio_file),
                language=settings.WHISPER_LANGUAGE,
                verbose=True
            )
            return result
        
        # 在執行緒中運行以避免阻塞
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(None, _transcribe)
        
        # 格式化結果
        transcript = {
            'text': result['text'].strip(),
            'segments': [
                {
                    'start': segment['start'],
                    'end': segment['end'],
                    'text': segment['text'].strip()
                }
                for segment in result.get('segments', [])
            ],
            'language': result.get('language', settings.WHISPER_LANGUAGE),
            'engine': 'whisper',
            'model': settings.WHISPER_MODEL
        }
        
        return transcript
    
    async def _process_with_faster_whisper(self, audio_file):
        """使用 Faster-Whisper 處理音訊"""
        def _transcribe():
            segments, info = self.model.transcribe(
                str(audio_file),
                language=settings.WHISPER_LANGUAGE,
                beam_size=5
            )
            return list(segments), info
        
        # 在執行緒中運行以避免阻塞
        loop = asyncio.get_event_loop()
        segments, info = await loop.run_in_executor(None, _transcribe)
        
        # 格式化結果
        full_text = ""
        formatted_segments = []
        
        for segment in segments:
            full_text += segment.text + " "
            formatted_segments.append({
                'start': segment.start,
                'end': segment.end,
                'text': segment.text.strip()
            })
        
        transcript = {
            'text': full_text.strip(),
            'segments': formatted_segments,
            'language': info.language,
            'engine': 'faster-whisper',
            'model': settings.WHISPER_MODEL
        }
        
        return transcript
    
    async def _process_with_vosk(self, audio_file):
        """使用 Vosk 處理音訊"""
        import vosk
        import json
        
        def _transcribe():
            # 讀取音訊檔案
            wf = wave.open(str(audio_file), 'rb')
            
            # 建立識別器
            rec = vosk.KaldiRecognizer(self.model, wf.getframerate())
            rec.SetWords(True)
            
            results = []
            full_text = ""
            
            # 處理音訊數據
            while True:
                data = wf.readframes(4000)
                if len(data) == 0:
                    break
                
                if rec.AcceptWaveform(data):
                    result = json.loads(rec.Result())
                    if result.get('text'):
                        results.append(result)
                        full_text += result['text'] + " "
            
            # 獲取最終結果
            final_result = json.loads(rec.FinalResult())
            if final_result.get('text'):
                results.append(final_result)
                full_text += final_result['text']
            
            wf.close()
            return results, full_text.strip()
        
        # 在執行緒中運行以避免阻塞
        loop = asyncio.get_event_loop()
        results, full_text = await loop.run_in_executor(None, _transcribe)
        
        # 格式化結果
        segments = []
        for result in results:
            if 'result' in result:
                for word in result['result']:
                    segments.append({
                        'start': word.get('start', 0),
                        'end': word.get('end', 0),
                        'text': word.get('word', '')
                    })
        
        transcript = {
            'text': full_text,
            'segments': segments,
            'language': settings.WHISPER_LANGUAGE,
            'engine': 'vosk',
            'model': 'vosk-model'
        }
        
        return transcript
    
    async def _save_transcript(self, audio_file, transcript):
        """保存轉錄結果"""
        # 建立轉錄檔案名稱
        transcript_file = settings.TRANSCRIPTS_DIR / f"{audio_file.stem}_transcript.json"
        
        # 添加元數據
        transcript_data = {
            'audio_file': str(audio_file),
            'timestamp': datetime.now().isoformat(),
            'engine': self.engine,
            'model': settings.WHISPER_MODEL,
            'language': settings.WHISPER_LANGUAGE,
            'transcript': transcript
        }
        
        try:
            with open(transcript_file, 'w', encoding='utf-8') as f:
                json.dump(transcript_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"轉錄結果已保存: {transcript_file}")
            
        except Exception as e:
            logger.error(f"保存轉錄結果失敗: {e}")
    
    def get_supported_engines(self):
        """獲取支援的引擎列表"""
        return ['whisper', 'faster-whisper', 'vosk']
    
    def get_engine_info(self):
        """獲取當前引擎資訊"""
        return {
            'engine': self.engine,
            'model': settings.WHISPER_MODEL,
            'language': settings.WHISPER_LANGUAGE,
            'initialized': self.model is not None
        }
