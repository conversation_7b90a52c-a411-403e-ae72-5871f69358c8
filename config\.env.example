# Discord Bot 設定
DISCORD_TOKEN=MTM4OTQwNTYyNDY3MjE5MDQ5NA.GSiNpF.HeVl5vqZwT1gLHOW9KDKp1S1hF19tEfSkm3xSA
OUTPUT_CHANNEL_ID=your_output_channel_id_here

# STT 引擎設定
STT_ENGINE=whisper  # 選項: whisper, faster-whisper, vosk
WHISPER_MODEL=base  # 選項: tiny, base, small, medium, large
WHISPER_LANGUAGE=zh  # 語言代碼，zh 為中文

# LLM 後端設定
MODEL_BACKEND=http://localhost:11434  # Ollama API URL
LLM_MODEL=gpt-oss:20b  # 使用的模型名稱
LLM_TEMPERATURE=0.7  # 生成溫度 (0.0-1.0)

# 錄音設定
RECORDING_FORMAT=wav  # 音訊格式
RECORDING_SAMPLE_RATE=16000  # 取樣率
RECORDING_CHANNELS=1  # 聲道數

# 檔案路徑設定
DATA_DIR=./data
RECORDINGS_DIR=./data/recordings
TRANSCRIPTS_DIR=./data/transcripts
MEETINGS_DIR=./data/meetings
PROMPTS_DIR=./prompts

# 日誌設定
LOG_LEVEL=INFO  # DEBUG, INFO, WARNING, ERROR
LOG_FILE=./logs/bot.log

# 其他設定
MAX_RECORDING_DURATION=3600  # 最大錄音時長（秒）
AUTO_LEAVE_TIMEOUT=300  # 自動離開語音頻道的超時時間（秒）
CHUNK_DURATION=30  # 音訊分段處理時長（秒）
