"""
Discord Meeting Bot 設定檔
"""
import os
from pathlib import Path
from dotenv import load_dotenv

# 載入環境變數
load_dotenv()

class Settings:
    """應用程式設定類別"""
    
    # 專案根目錄
    BASE_DIR = Path(__file__).parent.parent
    
    # Discord 設定
    DISCORD_TOKEN = os.getenv('DISCORD_TOKEN')
    OUTPUT_CHANNEL_ID = os.getenv('OUTPUT_CHANNEL_ID')
    
    # STT 引擎設定
    STT_ENGINE = os.getenv('STT_ENGINE', 'whisper')
    WHISPER_MODEL = os.getenv('WHISPER_MODEL', 'base')
    WHISPER_LANGUAGE = os.getenv('WHISPER_LANGUAGE', 'zh')
    
    # LLM 後端設定
    MODEL_BACKEND = os.getenv('MODEL_BACKEND', 'http://localhost:11434')
    LLM_MODEL = os.getenv('LLM_MODEL', 'llama3:8b')
    LLM_TEMPERATURE = float(os.getenv('LLM_TEMPERATURE', '0.7'))
    
    # 錄音設定
    RECORDING_FORMAT = os.getenv('RECORDING_FORMAT', 'wav')
    RECORDING_SAMPLE_RATE = int(os.getenv('RECORDING_SAMPLE_RATE', '16000'))
    RECORDING_CHANNELS = int(os.getenv('RECORDING_CHANNELS', '1'))
    
    # 檔案路徑設定
    DATA_DIR = Path(os.getenv('DATA_DIR', BASE_DIR / 'data'))
    RECORDINGS_DIR = Path(os.getenv('RECORDINGS_DIR', DATA_DIR / 'recordings'))
    TRANSCRIPTS_DIR = Path(os.getenv('TRANSCRIPTS_DIR', DATA_DIR / 'transcripts'))
    MEETINGS_DIR = Path(os.getenv('MEETINGS_DIR', DATA_DIR / 'meetings'))
    PROMPTS_DIR = Path(os.getenv('PROMPTS_DIR', BASE_DIR / 'prompts'))
    
    # 日誌設定
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE = Path(os.getenv('LOG_FILE', BASE_DIR / 'logs' / 'bot.log'))
    
    # 其他設定
    MAX_RECORDING_DURATION = int(os.getenv('MAX_RECORDING_DURATION', '3600'))
    AUTO_LEAVE_TIMEOUT = int(os.getenv('AUTO_LEAVE_TIMEOUT', '300'))
    CHUNK_DURATION = int(os.getenv('CHUNK_DURATION', '30'))
    
    @classmethod
    def validate(cls):
        """驗證必要的設定是否存在"""
        required_settings = [
            'DISCORD_TOKEN',
            'OUTPUT_CHANNEL_ID'
        ]
        
        missing_settings = []
        for setting in required_settings:
            if not getattr(cls, setting):
                missing_settings.append(setting)
        
        if missing_settings:
            raise ValueError(f"缺少必要的環境變數: {', '.join(missing_settings)}")
        
        # 建立必要的目錄
        for directory in [cls.DATA_DIR, cls.RECORDINGS_DIR, cls.TRANSCRIPTS_DIR, 
                         cls.MEETINGS_DIR, cls.LOG_FILE.parent]:
            directory.mkdir(parents=True, exist_ok=True)
        
        return True

# 建立設定實例
settings = Settings()
