"""
輸出處理模組
處理會議紀錄的格式化和輸出
"""
import json
from datetime import datetime
from pathlib import Path
from loguru import logger

from config.settings import settings

class OutputManager:
    """輸出管理器"""
    
    def __init__(self):
        self.output_formats = ['markdown', 'txt', 'json', 'html']
    
    async def save_meeting_notes(self, meeting_id, transcript, summary):
        """保存會議紀錄"""
        logger.info(f"開始保存會議紀錄 - 會議ID: {meeting_id}")
        
        try:
            # 獲取會議資訊
            meeting_info = await self._load_meeting_info(meeting_id)
            
            # 建立完整的會議資料
            meeting_data = {
                'meeting_id': meeting_id,
                'meeting_info': meeting_info,
                'transcript': transcript,
                'summary': summary,
                'generated_at': datetime.now().isoformat()
            }
            
            # 保存不同格式的檔案
            output_files = {}
            
            # Markdown 格式（主要格式）
            markdown_file = await self._save_markdown(meeting_data)
            output_files['markdown'] = markdown_file
            
            # 純文字格式
            txt_file = await self._save_txt(meeting_data)
            output_files['txt'] = txt_file
            
            # JSON 格式（完整資料）
            json_file = await self._save_json(meeting_data)
            output_files['json'] = json_file
            
            # HTML 格式（可選）
            html_file = await self._save_html(meeting_data)
            output_files['html'] = html_file
            
            logger.info(f"會議紀錄保存完成 - 會議ID: {meeting_id}")
            logger.info(f"輸出檔案: {list(output_files.keys())}")
            
            # 返回主要的 Markdown 檔案
            return markdown_file
            
        except Exception as e:
            logger.error(f"保存會議紀錄失敗: {e}")
            raise
    
    async def _load_meeting_info(self, meeting_id):
        """載入會議資訊"""
        info_file = settings.RECORDINGS_DIR / f"{meeting_id}_info.json"
        
        try:
            if info_file.exists():
                with open(info_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                logger.warning(f"會議資訊檔案不存在: {info_file}")
                return {}
        except Exception as e:
            logger.error(f"載入會議資訊失敗: {e}")
            return {}
    
    async def _save_markdown(self, meeting_data):
        """保存 Markdown 格式"""
        meeting_id = meeting_data['meeting_id']
        meeting_info = meeting_data['meeting_info']
        summary = meeting_data['summary']
        
        # 格式化會議資訊
        date = datetime.now().strftime('%Y/%m/%d')
        participants = ', '.join(meeting_info.get('participants', ['未知']))
        duration_seconds = meeting_info.get('duration_seconds', 0)
        duration = f"{int(duration_seconds // 60)}分{int(duration_seconds % 60)}秒"
        
        # 建立 Markdown 內容
        markdown_content = f"""# 會議紀錄

**會議 ID:** `{meeting_id}`  
**日期:** {date}  
**與會人員:** {participants}  
**會議時長:** {duration}  
**生成時間:** {datetime.now().strftime('%Y/%m/%d %H:%M:%S')}

---

{summary}

---

## 原始轉錄文字

```
{meeting_data['transcript'].get('text', '無轉錄文字')}
```

---

## 技術資訊

- **STT 引擎:** {meeting_data['transcript'].get('engine', '未知')}
- **STT 模型:** {meeting_data['transcript'].get('model', '未知')}
- **語言:** {meeting_data['transcript'].get('language', '未知')}
- **音訊片段數:** {len(meeting_data['transcript'].get('segments', []))}

---

*此會議紀錄由 Discord Meeting Bot 自動生成*
"""
        
        # 保存檔案
        output_file = settings.MEETINGS_DIR / f"{meeting_id}.md"
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(markdown_content)
            
            logger.info(f"Markdown 檔案已保存: {output_file}")
            return output_file
            
        except Exception as e:
            logger.error(f"保存 Markdown 檔案失敗: {e}")
            raise
    
    async def _save_txt(self, meeting_data):
        """保存純文字格式"""
        meeting_id = meeting_data['meeting_id']
        meeting_info = meeting_data['meeting_info']
        summary = meeting_data['summary']
        
        # 格式化會議資訊
        date = datetime.now().strftime('%Y/%m/%d')
        participants = ', '.join(meeting_info.get('participants', ['未知']))
        duration_seconds = meeting_info.get('duration_seconds', 0)
        duration = f"{int(duration_seconds // 60)}分{int(duration_seconds % 60)}秒"
        
        # 建立純文字內容
        txt_content = f"""會議紀錄

會議 ID: {meeting_id}
日期: {date}
與會人員: {participants}
會議時長: {duration}
生成時間: {datetime.now().strftime('%Y/%m/%d %H:%M:%S')}

{'='*50}

{summary}

{'='*50}

原始轉錄文字:

{meeting_data['transcript'].get('text', '無轉錄文字')}

{'='*50}

技術資訊:
- STT 引擎: {meeting_data['transcript'].get('engine', '未知')}
- STT 模型: {meeting_data['transcript'].get('model', '未知')}
- 語言: {meeting_data['transcript'].get('language', '未知')}
- 音訊片段數: {len(meeting_data['transcript'].get('segments', []))}

此會議紀錄由 Discord Meeting Bot 自動生成
"""
        
        # 保存檔案
        output_file = settings.MEETINGS_DIR / f"{meeting_id}.txt"
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(txt_content)
            
            logger.info(f"TXT 檔案已保存: {output_file}")
            return output_file
            
        except Exception as e:
            logger.error(f"保存 TXT 檔案失敗: {e}")
            raise
    
    async def _save_json(self, meeting_data):
        """保存 JSON 格式（完整資料）"""
        meeting_id = meeting_data['meeting_id']
        
        # 保存檔案
        output_file = settings.MEETINGS_DIR / f"{meeting_id}_complete.json"
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(meeting_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"JSON 檔案已保存: {output_file}")
            return output_file
            
        except Exception as e:
            logger.error(f"保存 JSON 檔案失敗: {e}")
            raise
    
    async def _save_html(self, meeting_data):
        """保存 HTML 格式"""
        meeting_id = meeting_data['meeting_id']
        meeting_info = meeting_data['meeting_info']
        summary = meeting_data['summary']
        
        # 格式化會議資訊
        date = datetime.now().strftime('%Y/%m/%d')
        participants = ', '.join(meeting_info.get('participants', ['未知']))
        duration_seconds = meeting_info.get('duration_seconds', 0)
        duration = f"{int(duration_seconds // 60)}分{int(duration_seconds % 60)}秒"
        
        # 建立 HTML 內容
        html_content = f"""<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>會議紀錄 - {meeting_id}</title>
    <style>
        body {{ font-family: 'Microsoft JhengHei', Arial, sans-serif; line-height: 1.6; margin: 40px; }}
        .header {{ background-color: #f4f4f4; padding: 20px; border-radius: 5px; margin-bottom: 20px; }}
        .summary {{ background-color: #e8f4fd; padding: 15px; border-left: 4px solid #2196F3; margin: 20px 0; }}
        .transcript {{ background-color: #f9f9f9; padding: 15px; border-radius: 5px; white-space: pre-wrap; }}
        .footer {{ margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; }}
        h1 {{ color: #333; }}
        h2 {{ color: #555; }}
        .info-table {{ width: 100%; border-collapse: collapse; }}
        .info-table td {{ padding: 8px; border-bottom: 1px solid #eee; }}
        .info-table td:first-child {{ font-weight: bold; width: 120px; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>會議紀錄</h1>
        <table class="info-table">
            <tr><td>會議 ID:</td><td>{meeting_id}</td></tr>
            <tr><td>日期:</td><td>{date}</td></tr>
            <tr><td>與會人員:</td><td>{participants}</td></tr>
            <tr><td>會議時長:</td><td>{duration}</td></tr>
            <tr><td>生成時間:</td><td>{datetime.now().strftime('%Y/%m/%d %H:%M:%S')}</td></tr>
        </table>
    </div>
    
    <div class="summary">
        <h2>會議摘要</h2>
        <div>{summary.replace(chr(10), '<br>')}</div>
    </div>
    
    <h2>原始轉錄文字</h2>
    <div class="transcript">{meeting_data['transcript'].get('text', '無轉錄文字')}</div>
    
    <div class="footer">
        <h3>技術資訊</h3>
        <ul>
            <li>STT 引擎: {meeting_data['transcript'].get('engine', '未知')}</li>
            <li>STT 模型: {meeting_data['transcript'].get('model', '未知')}</li>
            <li>語言: {meeting_data['transcript'].get('language', '未知')}</li>
            <li>音訊片段數: {len(meeting_data['transcript'].get('segments', []))}</li>
        </ul>
        <p><em>此會議紀錄由 Discord Meeting Bot 自動生成</em></p>
    </div>
</body>
</html>"""
        
        # 保存檔案
        output_file = settings.MEETINGS_DIR / f"{meeting_id}.html"
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            logger.info(f"HTML 檔案已保存: {output_file}")
            return output_file
            
        except Exception as e:
            logger.error(f"保存 HTML 檔案失敗: {e}")
            raise
    
    def get_meeting_files(self, meeting_id):
        """獲取會議相關的所有檔案"""
        files = {
            'markdown': settings.MEETINGS_DIR / f"{meeting_id}.md",
            'txt': settings.MEETINGS_DIR / f"{meeting_id}.txt",
            'json': settings.MEETINGS_DIR / f"{meeting_id}_complete.json",
            'html': settings.MEETINGS_DIR / f"{meeting_id}.html",
            'audio': settings.RECORDINGS_DIR / f"{meeting_id}.wav",
            'transcript': settings.TRANSCRIPTS_DIR / f"{meeting_id}_transcript.json",
            'summary': settings.MEETINGS_DIR / f"{meeting_id}_summary.json"
        }
        
        # 只返回存在的檔案
        existing_files = {k: v for k, v in files.items() if v.exists()}
        return existing_files
    
    def get_supported_formats(self):
        """獲取支援的輸出格式"""
        return self.output_formats
