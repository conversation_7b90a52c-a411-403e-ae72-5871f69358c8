@echo off
echo Discord Meeting Bot 安裝腳本
echo ============================

echo 1. 檢查 Python 版本...
python --version
if %errorlevel% neq 0 (
    echo 錯誤: 找不到 Python，請先安裝 Python 3.8+
    pause
    exit /b 1
)

echo 2. 建立虛擬環境...
python -m venv venv
if %errorlevel% neq 0 (
    echo 錯誤: 無法建立虛擬環境
    pause
    exit /b 1
)

echo 3. 啟動虛擬環境...
call venv\Scripts\activate.bat

echo 4. 升級 pip...
python -m pip install --upgrade pip

echo 5. 安裝依賴套件...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo 錯誤: 安裝依賴套件失敗
    pause
    exit /b 1
)

echo 6. 複製環境設定檔...
if not exist config\.env (
    copy config\.env.example config\.env
    echo 請編輯 config\.env 檔案，設定您的 Discord Token 和其他參數
)

echo 7. 檢查 FFmpeg...
ffmpeg -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 警告: 找不到 FFmpeg，請從 https://ffmpeg.org/download.html 下載並安裝
)

echo.
echo 安裝完成！
echo.
echo 下一步：
echo 1. 編輯 config\.env 檔案，設定您的 Discord Token
echo 2. 確保 Ollama 正在運行 (http://localhost:11434)
echo 3. 執行 python bot.py 啟動 Bot
echo.
pause
