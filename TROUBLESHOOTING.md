# 故障排除指南

## 🔧 常見問題與解決方案

### Discord 連接問題

#### 問題：Bot 無法啟動或連接失敗
**症狀：**
```
discord.errors.LoginFailure: Improper token has been passed.
```

**解決方案：**
1. 檢查 `.env` 檔案中的 `DISCORD_TOKEN` 是否正確
2. 確認 Token 沒有多餘的空格或引號
3. 重新生成 Bot Token：
   - 前往 Discord Developer Portal
   - 選擇您的應用程式 > Bot
   - 點擊 "Reset Token"

#### 問題：Bot 無法加入語音頻道
**症狀：**
```
discord.errors.ClientException: Already connected to a voice channel.
```

**解決方案：**
1. 使用 `!leave` 指令讓 Bot 離開當前頻道
2. 重新啟動 Bot
3. 檢查 Bot 權限是否包含 "Connect" 和 "Speak"

### 錄音相關問題

#### 問題：錄音檔案為空或損壞
**症狀：**
- 生成的 `.wav` 檔案大小為 0
- STT 處理時報錯

**解決方案：**
1. 檢查 FFmpeg 安裝：
   ```bash
   ffmpeg -version
   ```
2. 確認語音頻道中有人說話
3. 檢查 Bot 的錄音權限
4. 重新啟動 Bot 並重試

#### 問題：音訊品質差
**解決方案：**
1. 調整錄音設定（在 `.env` 中）：
   ```env
   RECORDING_SAMPLE_RATE=48000
   RECORDING_CHANNELS=2
   ```
2. 確保網路連接穩定
3. 建議參與者使用耳機減少回音

### STT 語音轉文字問題

#### 問題：Whisper 模型下載失敗
**症狀：**
```
urllib.error.URLError: <urlopen error [Errno 11001] getaddrinfo failed>
```

**解決方案：**
1. 檢查網路連接
2. 手動下載模型：
   ```python
   import whisper
   model = whisper.load_model("base")
   ```
3. 使用較小的模型（如 "tiny"）

#### 問題：STT 轉換速度慢
**解決方案：**
1. 使用 Faster-Whisper：
   ```env
   STT_ENGINE=faster-whisper
   ```
2. 如有 GPU，確保 CUDA 已安裝
3. 使用較小的模型：
   ```env
   WHISPER_MODEL=tiny
   ```

#### 問題：中文識別準確度低
**解決方案：**
1. 確認語言設定：
   ```env
   WHISPER_LANGUAGE=zh
   ```
2. 使用較大的模型：
   ```env
   WHISPER_MODEL=medium
   ```
3. 確保音訊品質良好

### LLM 摘要生成問題

#### 問題：Ollama 連接失敗
**症狀：**
```
aiohttp.client_exceptions.ClientConnectorError: Cannot connect to host localhost:11434
```

**解決方案：**
1. 確認 Ollama 正在運行：
   ```bash
   ollama serve
   ```
2. 檢查端口是否被佔用：
   ```bash
   netstat -an | grep 11434
   ```
3. 測試 API 連接：
   ```bash
   curl http://localhost:11434/api/tags
   ```

#### 問題：模型未找到
**症狀：**
```
{"error":"model 'llama3:8b' not found"}
```

**解決方案：**
1. 下載所需模型：
   ```bash
   ollama pull llama3:8b
   ```
2. 查看可用模型：
   ```bash
   ollama list
   ```
3. 更新 `.env` 中的模型名稱

#### 問題：摘要生成超時
**解決方案：**
1. 使用較小的模型
2. 增加超時時間（修改 `llm.py` 中的 timeout 設定）
3. 確保系統有足夠的 RAM

### 檔案系統問題

#### 問題：權限不足
**症狀：**
```
PermissionError: [Errno 13] Permission denied
```

**解決方案：**
1. 檢查目錄權限：
   ```bash
   ls -la data/
   ```
2. 修正權限：
   ```bash
   chmod -R 755 data/
   ```
3. 確認 Bot 程序有寫入權限

#### 問題：磁碟空間不足
**解決方案：**
1. 清理舊的錄音檔案
2. 設定自動清理腳本
3. 監控磁碟使用量

### 效能問題

#### 問題：記憶體使用量過高
**解決方案：**
1. 使用較小的 STT 模型
2. 限制同時處理的音訊長度
3. 定期重啟 Bot

#### 問題：處理速度慢
**解決方案：**
1. 使用 GPU 加速（如果可用）
2. 調整音訊分段大小：
   ```env
   CHUNK_DURATION=15
   ```
3. 使用 SSD 儲存

## 🔍 診斷工具

### 檢查系統狀態
```bash
# 檢查 Python 版本
python --version

# 檢查已安裝套件
pip list

# 檢查 FFmpeg
ffmpeg -version

# 檢查 GPU 支援（如果有）
nvidia-smi
```

### 測試各模組
```python
# 測試 STT 模組
python -c "
from modules.stt import STTProcessor
stt = STTProcessor()
print(f'STT 引擎: {stt.get_engine_info()}')
"

# 測試 LLM 模組
python -c "
import asyncio
from modules.llm import LLMProcessor
async def test():
    llm = LLMProcessor()
    models = await llm.get_available_models()
    print(f'可用模型: {models}')
asyncio.run(test())
"
```

### 日誌分析
```bash
# 查看最近的錯誤
grep -i error logs/bot.log | tail -10

# 查看特定時間的日誌
grep "2025-09-13 19:" logs/bot.log

# 監控即時日誌
tail -f logs/bot.log
```

## 📞 獲取幫助

如果以上解決方案都無法解決您的問題：

1. **收集診斷資訊：**
   - Bot 版本
   - 作業系統版本
   - Python 版本
   - 錯誤訊息的完整日誌

2. **提交 Issue：**
   - 前往 GitHub Issues
   - 使用問題模板
   - 提供詳細的重現步驟

3. **社群支援：**
   - Discord 伺服器
   - 討論區

## 🔄 重置和重新安裝

### 完全重置
```bash
# 停止所有服務
docker-compose down

# 清理資料
rm -rf data/recordings/*
rm -rf data/transcripts/*
rm -rf data/meetings/*
rm -rf logs/*

# 重新安裝依賴
pip install -r requirements.txt --force-reinstall
```

### 重新建立虛擬環境
```bash
# 刪除舊環境
rm -rf venv/

# 建立新環境
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或 venv\Scripts\activate  # Windows

# 重新安裝
pip install -r requirements.txt
```
