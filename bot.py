"""
Discord Meeting Notes Bot
主要 Bot 程式，處理 Discord 連接和指令
"""
import asyncio
import discord
from discord.ext import commands
from loguru import logger
import sys
from pathlib import Path

# 添加專案根目錄到 Python 路徑
sys.path.append(str(Path(__file__).parent))

from config.settings import settings
from modules.recorder import VoiceRecorder
from modules.stt import STTProcessor
from modules.llm import LLMProcessor
from modules.output import OutputManager

class MeetingBot(commands.Bot):
    """Discord 會議紀錄 Bot"""
    
    def __init__(self):
        # 設定 Bot 意圖
        intents = discord.Intents.default()
        intents.message_content = True
        intents.voice_states = True
        
        super().__init__(
            command_prefix='!',
            intents=intents,
            description='Discord 會議紀錄 Bot'
        )
        
        # 初始化模組
        self.recorder = VoiceRecorder()
        self.stt_processor = STTProcessor()
        self.llm_processor = LLMProcessor()
        self.output_manager = OutputManager()
        
        # 會議狀態
        self.is_recording = False
        self.current_meeting = None
        self.voice_client = None
        
    async def on_ready(self):
        """Bot 準備就緒時的回調"""
        logger.info(f'{self.user} 已成功連接到 Discord!')
        logger.info(f'Bot 正在 {len(self.guilds)} 個伺服器中運行')
        
        # 設定 Bot 狀態
        await self.change_presence(
            activity=discord.Activity(
                type=discord.ActivityType.listening,
                name="!help 查看指令"
            )
        )
    
    async def on_command_error(self, ctx, error):
        """指令錯誤處理"""
        if isinstance(error, commands.CommandNotFound):
            await ctx.send("❌ 找不到該指令，請使用 `!help` 查看可用指令")
        elif isinstance(error, commands.MissingRequiredArgument):
            await ctx.send(f"❌ 缺少必要參數: {error.param}")
        else:
            logger.error(f"指令錯誤: {error}")
            await ctx.send(f"❌ 發生錯誤: {str(error)}")

# 建立 Bot 實例
bot = MeetingBot()

@bot.command(name='join', help='讓 Bot 加入語音頻道開始錄音')
async def join_voice(ctx):
    """加入語音頻道並開始錄音"""
    if ctx.author.voice is None:
        await ctx.send("❌ 你需要先加入一個語音頻道！")
        return
    
    if bot.is_recording:
        await ctx.send("❌ Bot 目前正在錄音中，請先使用 `!leave` 停止錄音")
        return
    
    try:
        # 加入語音頻道
        channel = ctx.author.voice.channel
        bot.voice_client = await channel.connect()
        
        # 開始錄音
        meeting_id = await bot.recorder.start_recording(bot.voice_client, channel)
        bot.current_meeting = meeting_id
        bot.is_recording = True
        
        embed = discord.Embed(
            title="🎙️ 開始錄音",
            description=f"已加入 **{channel.name}** 並開始錄音\n會議 ID: `{meeting_id}`",
            color=discord.Color.green()
        )
        await ctx.send(embed=embed)
        
        logger.info(f"開始錄音 - 頻道: {channel.name}, 會議ID: {meeting_id}")
        
    except Exception as e:
        logger.error(f"加入語音頻道失敗: {e}")
        await ctx.send(f"❌ 加入語音頻道失敗: {str(e)}")

@bot.command(name='leave', help='停止錄音並生成會議紀錄')
async def leave_voice(ctx):
    """離開語音頻道並處理會議紀錄"""
    if not bot.is_recording:
        await ctx.send("❌ Bot 目前沒有在錄音")
        return
    
    try:
        # 停止錄音
        audio_file = await bot.recorder.stop_recording()
        bot.is_recording = False
        
        # 離開語音頻道
        if bot.voice_client:
            await bot.voice_client.disconnect()
            bot.voice_client = None
        
        embed = discord.Embed(
            title="⏹️ 停止錄音",
            description="正在處理會議紀錄，請稍候...",
            color=discord.Color.orange()
        )
        message = await ctx.send(embed=embed)
        
        # 處理音訊轉文字
        transcript = await bot.stt_processor.process_audio(audio_file)
        
        # 生成會議摘要
        meeting_summary = await bot.llm_processor.generate_summary(
            transcript, 
            bot.current_meeting
        )
        
        # 輸出會議紀錄
        output_file = await bot.output_manager.save_meeting_notes(
            bot.current_meeting,
            transcript,
            meeting_summary
        )
        
        # 更新訊息
        embed = discord.Embed(
            title="✅ 會議紀錄完成",
            description=f"會議 ID: `{bot.current_meeting}`",
            color=discord.Color.green()
        )
        embed.add_field(
            name="📄 會議摘要",
            value=meeting_summary[:1000] + "..." if len(meeting_summary) > 1000 else meeting_summary,
            inline=False
        )
        
        await message.edit(embed=embed)
        
        # 發送會議紀錄檔案
        if output_file.exists():
            await ctx.send(file=discord.File(output_file))
        
        logger.info(f"會議紀錄完成 - 會議ID: {bot.current_meeting}")
        bot.current_meeting = None
        
    except Exception as e:
        logger.error(f"處理會議紀錄失敗: {e}")
        await ctx.send(f"❌ 處理會議紀錄失敗: {str(e)}")

@bot.command(name='status', help='查看 Bot 目前狀態')
async def status(ctx):
    """查看 Bot 狀態"""
    embed = discord.Embed(
        title="🤖 Bot 狀態",
        color=discord.Color.blue()
    )
    
    embed.add_field(
        name="錄音狀態",
        value="🔴 錄音中" if bot.is_recording else "⚪ 待機中",
        inline=True
    )
    
    if bot.current_meeting:
        embed.add_field(
            name="當前會議",
            value=f"`{bot.current_meeting}`",
            inline=True
        )
    
    if bot.voice_client and bot.voice_client.channel:
        embed.add_field(
            name="語音頻道",
            value=bot.voice_client.channel.name,
            inline=True
        )
    
    await ctx.send(embed=embed)

@bot.command(name='help_meeting', help='顯示詳細使用說明')
async def help_meeting(ctx):
    """顯示詳細使用說明"""
    embed = discord.Embed(
        title="📖 Discord Meeting Bot 使用說明",
        description="自動記錄會議並生成結構化紀錄",
        color=discord.Color.blue()
    )
    
    embed.add_field(
        name="🎙️ 基本指令",
        value="`!join` - 加入語音頻道開始錄音\n"
              "`!leave` - 停止錄音並生成會議紀錄\n"
              "`!status` - 查看 Bot 狀態",
        inline=False
    )
    
    embed.add_field(
        name="📋 使用流程",
        value="1. 加入語音頻道\n"
              "2. 使用 `!join` 讓 Bot 開始錄音\n"
              "3. 進行會議討論\n"
              "4. 使用 `!leave` 停止錄音並獲得會議紀錄",
        inline=False
    )
    
    embed.add_field(
        name="⚠️ 注意事項",
        value="• Bot 需要語音頻道權限\n"
              "• 一次只能錄製一個會議\n"
              "• 會議紀錄會自動保存到檔案",
        inline=False
    )
    
    await ctx.send(embed=embed)

async def main():
    """主程式入口"""
    try:
        # 驗證設定
        settings.validate()
        
        # 設定日誌
        logger.remove()
        logger.add(
            sys.stderr,
            level=settings.LOG_LEVEL,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
        )
        logger.add(
            settings.LOG_FILE,
            level=settings.LOG_LEVEL,
            rotation="1 day",
            retention="7 days"
        )
        
        logger.info("啟動 Discord Meeting Bot...")
        
        # 啟動 Bot
        await bot.start(settings.DISCORD_TOKEN)
        
    except Exception as e:
        logger.error(f"Bot 啟動失敗: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
